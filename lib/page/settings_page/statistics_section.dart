import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/page/home_page/statistics_page.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:flutter/material.dart';

class StatisticsSection extends StatelessWidget {
  const StatisticsSection({
    super.key,
    this.onNavigate,
  });

  final void Function(Widget page, String title)? onNavigate;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(AdaptiveIcons.barChart),
      title: Text(L10n.of(context).navBar_statistics),
      trailing: Icon(AdaptiveIcons.chevronRight),
      onTap: () {
        if (onNavigate != null) {
          // Use profile navigation callback if available
          onNavigate!(
            const StatisticPage(),
            L10n.of(context).navBar_statistics,
          );
        } else {
          // Use traditional full-screen navigation
          AdaptiveNavigation.push(
            context,
            Scaffold(
              appBar: AppBar(
                leading: IconButton(
                  icon: Icon(AdaptiveIcons.back),
                  tooltip: 'Back to settings',
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
                title: Text(L10n.of(context).navBar_statistics),
              ),
              body: const StatisticPage(),
            ),
          );
        }
      },
    );
  }
}
