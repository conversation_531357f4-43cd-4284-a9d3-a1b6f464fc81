import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/page/settings_page/ai.dart';
import 'package:dasso_reader/page/settings_page/advanced.dart';
import 'package:dasso_reader/page/settings_page/appearance.dart';
import 'package:dasso_reader/page/settings_page/narrate.dart';
import 'package:dasso_reader/page/settings_page/reading.dart';
import 'package:dasso_reader/page/settings_page/settings_page.dart';
import 'package:dasso_reader/page/settings_page/storege.dart';
import 'package:dasso_reader/page/settings_page/sync.dart';
import 'package:dasso_reader/page/settings_page/translate.dart';
import 'package:dasso_reader/widgets/settings/about.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:dasso_reader/widgets/common/adaptive_components.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:flutter/material.dart';

class MoreSettings extends StatelessWidget {
  const MoreSettings({
    super.key,
    this.onNavigate,
  });

  final void Function(Widget page, String title)? onNavigate;

  @override
  Widget build(BuildContext context) {
    void handleTap() {
      if (onNavigate != null) {
        // Use profile navigation callback if available
        onNavigate!(
          const SubMoreSettings(),
          L10n.of(context).settings_moreSettings,
        );
      } else {
        // Use traditional full-screen navigation
        AdaptiveNavigation.push(
          context,
          const SubMoreSettings(),
        );
      }
    }

    return SemanticHelpers.listItem(
      child: AdaptiveListTile(
        leading: Icon(AdaptiveIcons.settings),
        title: Text(L10n.of(context).settings_moreSettings),
        trailing: Icon(AdaptiveIcons.chevronRight),
        onTap: handleTap,
      ),
      label: L10n.of(context).settings_moreSettings,
      onTap: handleTap,
    );
  }
}

class SubMoreSettings extends StatefulWidget {
  const SubMoreSettings({super.key});

  @override
  State<SubMoreSettings> createState() => _SubMoreSettingsState();
}

class _SubMoreSettingsState extends State<SubMoreSettings> {
  int selectedIndex = 0;
  Widget? settingsDetail;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        List<Map<String, dynamic>> settings = [
          {
            'title': L10n.of(context).settings_appearance,
            'icon': AdaptiveIcons.colorLens,
            'sections': const AppearanceSetting(),
            'subtitles': [
              L10n.of(context).settings_appearance_theme,
              L10n.of(context).settings_appearance_display,
              L10n.of(context).settings_bookshelf_cover,
            ],
          },
          {
            'title': L10n.of(context).settings_reading,
            'icon': AdaptiveIcons.book,
            'sections': const ReadingSettings(),
            'subtitles': [
              L10n.of(context).reading_page_reading,
              L10n.of(context).download_fonts,
              L10n.of(context).reading_page_style,
              L10n.of(context).reading_page_other,
            ],
          },
          {
            'title': L10n.of(context).settings_sync,
            'icon': AdaptiveIcons.sync,
            'sections': const SyncSetting(),
            'subtitles': [
              L10n.of(context).settings_sync_webdav,
              L10n.of(context).export_and_import,
            ],
          },
          {
            'title': L10n.of(context).settings_narrate,
            'icon': AdaptiveIcons.headphones,
            'sections': const NarrateSettings(),
            'subtitles': [
              L10n.of(context).settings_narrate_voice,
              L10n.of(context).settings_narrate_voice_model,
            ],
          },
          {
            'title': L10n.of(context).settings_translate,
            'icon': AdaptiveIcons.translate,
            'sections': const TranslateSetting(),
            'subtitles': [
              L10n.of(context).settings_translate,
            ],
          },
          {
            'title': L10n.of(context).settings_ai,
            'icon': AdaptiveIcons.autoAwesome,
            'sections': const AISettings(),
            'subtitles': [
              L10n.of(context).settings_ai_services,
              L10n.of(context).settings_ai_prompt,
            ],
          },
          {
            'title': L10n.of(context).storage,
            'icon': AdaptiveIcons.storage,
            'sections': const StorageSettings(),
            'subtitles': [
              L10n.of(context).storage_info,
              L10n.of(context).storage_data_file_details,
            ],
          },
          {
            'title': L10n.of(context).settings_advanced,
            'icon': AdaptiveIcons.shield,
            'sections': const AdvancedSetting(),
            'subtitles': [
              L10n.of(context).settings_advanced_log,
            ],
          },
        ];

        settingsDetail ??= SettingsPageBody(
          isMobile: false,
          title: settings[0]['title'] as String? ?? '',
          sections:
              settings[0]['sections'] as Widget? ?? const SizedBox.shrink(),
        );

        void setDetail(Widget detail, int id) {
          setState(() {
            settingsDetail = detail;
            selectedIndex = id;
          });
        }

        Widget settingsList(bool isMobile) {
          return ListView.builder(
            itemCount: settings.length + 1,
            itemBuilder: (context, index) {
              if (index == settings.length) {
                return const About(leadingColor: true);
              }
              return SettingsPageBuilder(
                isMobile: isMobile,
                id: index,
                selectedIndex: selectedIndex,
                setDetail: setDetail,
                icon: Icon(
                  settings[index]['icon'] as IconData? ??
                      AdaptiveIcons.settings,
                  color: Theme.of(context).colorScheme.primary,
                  semanticLabel:
                      '${settings[index]['title'] as String? ?? ''} settings',
                ),
                title: settings[index]['title'] as String? ?? '',
                sections: settings[index]['sections'] as Widget? ??
                    const SizedBox.shrink(),
                subTitles: (settings[index]['subtitles'] as List<dynamic>?)
                        ?.cast<String>() ??
                    <String>[],
              );
            },
          );
        }

        if (DesignSystem.isTablet(context)) {
          return Row(
            children: [
              Expanded(
                flex: 1,
                child: settingsList(false),
              ),
              const VerticalDivider(thickness: 1, width: 1),
              Expanded(
                flex: 2,
                child: settingsDetail!,
              ),
            ],
          );
        } else {
          return settingsList(true);
        }
      },
    );
  }
}
